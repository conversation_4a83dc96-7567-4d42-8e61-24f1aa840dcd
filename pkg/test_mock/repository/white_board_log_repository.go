// Code generated by MockGen. DO NOT EDIT.
// Source: white_board_log_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	entity "denkaru-server/pkg/repository/model/entity"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIWhiteBoardLogRepository is a mock of IWhiteBoardLogRepository interface.
type MockIWhiteBoardLogRepository struct {
	ctrl     *gomock.Controller
	recorder *MockIWhiteBoardLogRepositoryMockRecorder
}

// MockIWhiteBoardLogRepositoryMockRecorder is the mock recorder for MockIWhiteBoardLogRepository.
type MockIWhiteBoardLogRepositoryMockRecorder struct {
	mock *MockIWhiteBoardLogRepository
}

// NewMockIWhiteBoardLogRepository creates a new mock instance.
func NewMockIWhiteBoardLogRepository(ctrl *gomock.Controller) *MockIWhiteBoardLogRepository {
	mock := &MockIWhiteBoardLogRepository{ctrl: ctrl}
	mock.recorder = &MockIWhiteBoardLogRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWhiteBoardLogRepository) EXPECT() *MockIWhiteBoardLogRepositoryMockRecorder {
	return m.recorder
}

// CreateWhiteBoardLog mocks base method.
func (m *MockIWhiteBoardLogRepository) CreateWhiteBoardLog(ctx context.Context, hospitalID int, contentBefore, contentAfter string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWhiteBoardLog", ctx, hospitalID, contentBefore, contentAfter)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateWhiteBoardLog indicates an expected call of CreateWhiteBoardLog.
func (mr *MockIWhiteBoardLogRepositoryMockRecorder) CreateWhiteBoardLog(ctx, hospitalID, contentBefore, contentAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWhiteBoardLog", reflect.TypeOf((*MockIWhiteBoardLogRepository)(nil).CreateWhiteBoardLog), ctx, hospitalID, contentBefore, contentAfter)
}

// CreateWhiteBoardLogWithUser mocks base method.
func (m *MockIWhiteBoardLogRepository) CreateWhiteBoardLogWithUser(ctx context.Context, hospitalID int, contentBefore, contentAfter, userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWhiteBoardLogWithUser", ctx, hospitalID, contentBefore, contentAfter, userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateWhiteBoardLogWithUser indicates an expected call of CreateWhiteBoardLogWithUser.
func (mr *MockIWhiteBoardLogRepositoryMockRecorder) CreateWhiteBoardLogWithUser(ctx, hospitalID, contentBefore, contentAfter, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWhiteBoardLogWithUser", reflect.TypeOf((*MockIWhiteBoardLogRepository)(nil).CreateWhiteBoardLogWithUser), ctx, hospitalID, contentBefore, contentAfter, userID)
}

// GetLastWhiteBoardLog mocks base method.
func (m *MockIWhiteBoardLogRepository) GetLastWhiteBoardLog(ctx context.Context, hospitalID int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastWhiteBoardLog", ctx, hospitalID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastWhiteBoardLog indicates an expected call of GetLastWhiteBoardLog.
func (mr *MockIWhiteBoardLogRepositoryMockRecorder) GetLastWhiteBoardLog(ctx, hospitalID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastWhiteBoardLog", reflect.TypeOf((*MockIWhiteBoardLogRepository)(nil).GetLastWhiteBoardLog), ctx, hospitalID)
}

// GetLastWhiteBoardLogDetail mocks base method.
func (m *MockIWhiteBoardLogRepository) GetLastWhiteBoardLogDetail(ctx context.Context, hospitalID int) (*entity.HpWhiteBoardLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastWhiteBoardLogDetail", ctx, hospitalID)
	ret0, _ := ret[0].(*entity.HpWhiteBoardLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastWhiteBoardLogDetail indicates an expected call of GetLastWhiteBoardLogDetail.
func (mr *MockIWhiteBoardLogRepositoryMockRecorder) GetLastWhiteBoardLogDetail(ctx, hospitalID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastWhiteBoardLogDetail", reflect.TypeOf((*MockIWhiteBoardLogRepository)(nil).GetLastWhiteBoardLogDetail), ctx, hospitalID)
}
