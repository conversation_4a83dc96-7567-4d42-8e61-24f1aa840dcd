// Code generated by MockGen. DO NOT EDIT.
// Source: white_board_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIWhiteBoardService is a mock of IWhiteBoardService interface.
type MockIWhiteBoardService struct {
	ctrl     *gomock.Controller
	recorder *MockIWhiteBoardServiceMockRecorder
}

// MockIWhiteBoardServiceMockRecorder is the mock recorder for MockIWhiteBoardService.
type MockIWhiteBoardServiceMockRecorder struct {
	mock *MockIWhiteBoardService
}

// NewMockIWhiteBoardService creates a new mock instance.
func NewMockIWhiteBoardService(ctrl *gomock.Controller) *MockIWhiteBoardService {
	mock := &MockIWhiteBoardService{ctrl: ctrl}
	mock.recorder = &MockIWhiteBoardServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWhiteBoardService) EXPECT() *MockIWhiteBoardServiceMockRecorder {
	return m.recorder
}

// CheckContentOutdated mocks base method.
func (m *MockIWhiteBoardService) CheckContentOutdated(ctx context.Context, hospitalID int, currentUserID, expectedContent string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckContentOutdated", ctx, hospitalID, currentUserID, expectedContent)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckContentOutdated indicates an expected call of CheckContentOutdated.
func (mr *MockIWhiteBoardServiceMockRecorder) CheckContentOutdated(ctx, hospitalID, currentUserID, expectedContent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckContentOutdated", reflect.TypeOf((*MockIWhiteBoardService)(nil).CheckContentOutdated), ctx, hospitalID, currentUserID, expectedContent)
}

// EditWhiteBoard mocks base method.
func (m *MockIWhiteBoardService) EditWhiteBoard(ctx context.Context, hospitalID int, contentBefore, contentAfter string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditWhiteBoard", ctx, hospitalID, contentBefore, contentAfter)
	ret0, _ := ret[0].(error)
	return ret0
}

// EditWhiteBoard indicates an expected call of EditWhiteBoard.
func (mr *MockIWhiteBoardServiceMockRecorder) EditWhiteBoard(ctx, hospitalID, contentBefore, contentAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditWhiteBoard", reflect.TypeOf((*MockIWhiteBoardService)(nil).EditWhiteBoard), ctx, hospitalID, contentBefore, contentAfter)
}

// EditWhiteBoardWithUser mocks base method.
func (m *MockIWhiteBoardService) EditWhiteBoardWithUser(ctx context.Context, hospitalID int, contentBefore, contentAfter, userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EditWhiteBoardWithUser", ctx, hospitalID, contentBefore, contentAfter, userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// EditWhiteBoardWithUser indicates an expected call of EditWhiteBoardWithUser.
func (mr *MockIWhiteBoardServiceMockRecorder) EditWhiteBoardWithUser(ctx, hospitalID, contentBefore, contentAfter, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EditWhiteBoardWithUser", reflect.TypeOf((*MockIWhiteBoardService)(nil).EditWhiteBoardWithUser), ctx, hospitalID, contentBefore, contentAfter, userID)
}

// GetWhiteBoard mocks base method.
func (m *MockIWhiteBoardService) GetWhiteBoard(ctx context.Context, hospitalID int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWhiteBoard", ctx, hospitalID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWhiteBoard indicates an expected call of GetWhiteBoard.
func (mr *MockIWhiteBoardServiceMockRecorder) GetWhiteBoard(ctx, hospitalID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWhiteBoard", reflect.TypeOf((*MockIWhiteBoardService)(nil).GetWhiteBoard), ctx, hospitalID)
}
