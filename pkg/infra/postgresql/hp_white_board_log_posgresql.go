package postgresql

import (
	"context"
	"denkaru-server/pkg/constant"
	"denkaru-server/pkg/repository"
	"denkaru-server/pkg/repository/model/entity"

	"gorm.io/gorm"
)

type hpWhiteBoardLogRepository struct {
	DB *gorm.DB
}

// NewHpWhiteBoardLogRepository initialize hpWhiteBoardLogRepository
func NewHpWhiteBoardLogRepository(db *gorm.DB) repository.IWhiteBoardLogRepository {
	return &hpWhiteBoardLogRepository{
		DB: db,
	}
}

func (r *hpWhiteBoardLogRepository) GetLastWhiteBoardLog(ctx context.Context, hospitalID int) (string, error) {
	var result entity.HpWhiteBoardLog
	err := r.DB.WithContext(ctx).
		Model(&entity.HpWhiteBoardLog{}).
		Where("hp_id = ?", hospitalID).
		Where("is_deleted = ?", constant.StatusFalse).
		Last(&result).Error

	if err == gorm.ErrRecordNotFound {
		createErr := r.CreateWhiteBoardLog(ctx, hospitalID, "", "")
		if createErr != nil {
			return "", createErr
		}

		return "", nil
	}

	if err != nil {
		return "", err
	}

	return result.ContentAfter, nil
}

func (r *hpWhiteBoardLogRepository) GetLastWhiteBoardLogDetail(ctx context.Context, hospitalID int) (*entity.HpWhiteBoardLog, error) {
	var result entity.HpWhiteBoardLog
	err := r.DB.WithContext(ctx).
		Model(&entity.HpWhiteBoardLog{}).
		Where("hp_id = ?", hospitalID).
		Where("is_deleted = ?", constant.StatusFalse).
		Last(&result).Error

	if err == gorm.ErrRecordNotFound {
		createErr := r.CreateWhiteBoardLog(ctx, hospitalID, "", "")
		if createErr != nil {
			return nil, createErr
		}

		// Return empty log after creating initial record
		return &entity.HpWhiteBoardLog{
			HpID:          hospitalID,
			ContentBefore: "",
			ContentAfter:  "",
			CreatedBy:     "system",
			UpdatedBy:     "system",
		}, nil
	}

	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *hpWhiteBoardLogRepository) CreateWhiteBoardLog(ctx context.Context, hospitalID int, contentBefore string, contentAfter string) error {
	err := r.DB.WithContext(ctx).
		Create(&entity.HpWhiteBoardLog{
			HpID:          hospitalID,
			ContentBefore: contentBefore,
			ContentAfter:  contentAfter,
		}).Error

	if err != nil {
		return err
	}

	return nil
}

func (r *hpWhiteBoardLogRepository) CreateWhiteBoardLogWithUser(ctx context.Context, hospitalID int, contentBefore string, contentAfter string, userID string) error {
	err := r.DB.WithContext(ctx).
		Create(&entity.HpWhiteBoardLog{
			HpID:          hospitalID,
			ContentBefore: contentBefore,
			ContentAfter:  contentAfter,
			CreatedBy:     userID,
			UpdatedBy:     userID,
		}).Error

	if err != nil {
		return err
	}

	return nil
}
