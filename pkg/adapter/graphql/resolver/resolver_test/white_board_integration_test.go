package resolver_test

import (
	"denkaru-server/pkg/adapter/graphql/resolver"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mock_service "denkaru-server/pkg/test_mock/service"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// Test_WhiteBoard_Integration tests the complete flow of WhiteBoard operations
func Test_WhiteBoard_Integration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	hospitalID := 1
	staffID1 := 123
	staffID2 := 456

	// Setup sessions for two different users
	sess1 := &serviceModel.Session{
		HospitalID: &hospitalID,
		StaffID:    &staffID1,
	}
	sess2 := &serviceModel.Session{
		HospitalID: &hospitalID,
		StaffID:    &staffID2,
	}

	ctx1 := test_mock.GetTestContextWithSession(sess1)
	ctx2 := test_mock.GetTestContextWithSession(sess2)

	whiteBoardService := mock_service.NewMockIWhiteBoardService(ctrl)
	rslv := &resolver.Resolver{
		WhiteBoardService: whiteBoardService,
	}

	t.Run("Complete WhiteBoard workflow", func(t *testing.T) {
		// Step 1: User1 gets initial content
		whiteBoardService.EXPECT().GetWhiteBoard(gomock.Any(), hospitalID).Return("initial content", nil)
		content, err := rslv.Query().GetWhiteBoard(ctx1)
		assert.NoError(t, err)
		assert.Equal(t, "initial content", content)

		// Step 2: User1 checks if content is outdated (should be false initially)
		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_123", "initial content").Return(false, nil)
		isOutdated, err := rslv.Query().CheckContentOutdated(ctx1, "initial content")
		assert.NoError(t, err)
		assert.False(t, isOutdated)

		// Step 3: User1 edits the content
		whiteBoardService.EXPECT().EditWhiteBoardWithUser(gomock.Any(), hospitalID, "initial content", "user1 updated content", "staff_123").Return(nil)
		success, err := rslv.Mutation().EditWhiteBoard(ctx1, "initial content", "user1 updated content")
		assert.NoError(t, err)
		assert.True(t, success)

		// Step 4: User2 checks if their old content is outdated (should be true now)
		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_456", "initial content").Return(true, nil)
		isOutdated, err = rslv.Query().CheckContentOutdated(ctx2, "initial content")
		assert.NoError(t, err)
		assert.True(t, isOutdated, "User2's content should be outdated after User1's edit")

		// Step 5: User2 gets the latest content
		whiteBoardService.EXPECT().GetWhiteBoard(gomock.Any(), hospitalID).Return("user1 updated content", nil)
		latestContent, err := rslv.Query().GetWhiteBoard(ctx2)
		assert.NoError(t, err)
		assert.Equal(t, "user1 updated content", latestContent)

		// Step 6: User2 checks if the latest content is outdated (should be false now)
		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_456", "user1 updated content").Return(true, nil) // Still true because last editor was user1
		isOutdated, err = rslv.Query().CheckContentOutdated(ctx2, "user1 updated content")
		assert.NoError(t, err)
		assert.True(t, isOutdated, "Content should still be considered outdated because it was last edited by another user")
	})

	t.Run("Same user editing should not be outdated", func(t *testing.T) {
		// User1 checks their own content - should not be outdated
		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_123", "user1 updated content").Return(false, nil)
		isOutdated, err := rslv.Query().CheckContentOutdated(ctx1, "user1 updated content")
		assert.NoError(t, err)
		assert.False(t, isOutdated, "User should not see their own content as outdated")
	})

	t.Run("System user content should not be outdated", func(t *testing.T) {
		// Simulate system-created content
		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_123", "system content").Return(false, nil)
		isOutdated, err := rslv.Query().CheckContentOutdated(ctx1, "system content")
		assert.NoError(t, err)
		assert.False(t, isOutdated, "System-created content should not be considered outdated")
	})
}

// Test_WhiteBoard_EdgeCases tests edge cases and error scenarios
func Test_WhiteBoard_EdgeCases(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	hospitalID := 1
	staffID := 123

	sess := &serviceModel.Session{
		HospitalID: &hospitalID,
		StaffID:    &staffID,
	}
	ctx := test_mock.GetTestContextWithSession(sess)

	whiteBoardService := mock_service.NewMockIWhiteBoardService(ctrl)
	rslv := &resolver.Resolver{
		WhiteBoardService: whiteBoardService,
	}

	t.Run("Empty content handling", func(t *testing.T) {
		// Check empty content
		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_123", "").Return(false, nil)
		isOutdated, err := rslv.Query().CheckContentOutdated(ctx, "")
		assert.NoError(t, err)
		assert.False(t, isOutdated)

		// Edit with empty content
		whiteBoardService.EXPECT().EditWhiteBoardWithUser(gomock.Any(), hospitalID, "", "new content", "staff_123").Return(nil)
		success, err := rslv.Mutation().EditWhiteBoard(ctx, "", "new content")
		assert.NoError(t, err)
		assert.True(t, success)
	})

	t.Run("Large content handling", func(t *testing.T) {
		largeContent := string(make([]byte, 3000)) // 3KB content
		for i := range largeContent {
			largeContent = largeContent[:i] + "a" + largeContent[i+1:]
		}

		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_123", largeContent).Return(false, nil)
		isOutdated, err := rslv.Query().CheckContentOutdated(ctx, largeContent)
		assert.NoError(t, err)
		assert.False(t, isOutdated)
	})

	t.Run("Special characters handling", func(t *testing.T) {
		specialContent := "Content with special chars: 日本語, émojis 🎉, and symbols @#$%^&*()"

		whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), hospitalID, "staff_123", specialContent).Return(false, nil)
		isOutdated, err := rslv.Query().CheckContentOutdated(ctx, specialContent)
		assert.NoError(t, err)
		assert.False(t, isOutdated)
	})
}
