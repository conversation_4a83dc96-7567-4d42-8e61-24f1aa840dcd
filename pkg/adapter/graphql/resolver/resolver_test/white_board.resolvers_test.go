package resolver_test

import (
	"context"
	"denkaru-server/pkg/adapter/graphql/resolver"
	"denkaru-server/pkg/myerrors"
	serviceModel "denkaru-server/pkg/service/model"
	"denkaru-server/pkg/test_mock"
	mock_service "denkaru-server/pkg/test_mock/service"
	"fmt"
	"testing"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

var (
	whiteBoardHospitalID = 1
	whiteBoardStaffID    = 123
)

func Test_queryResolver_GetWhiteBoard(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	sess := &serviceModel.Session{
		HospitalID: &whiteBoardHospitalID,
		StaffID:    &whiteBoardStaffID,
	}
	ctx := test_mock.GetTestContextWithSession(sess)

	tests := []struct {
		name             string
		ctx              context.Context
		getWhiteBoardRes string
		getWhiteBoardErr error
		want             string
		wantErr          error
	}{
		{
			name:             "正常系：WhiteBoardの取得成功",
			ctx:              ctx,
			getWhiteBoardRes: "test content",
			want:             "test content",
		},
		{
			name:             "異常系：サービスエラー",
			ctx:              ctx,
			getWhiteBoardErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("service error")),
			wantErr:          myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("service error")),
		},
		{
			name:    "異常系：セッションなし",
			ctx:     context.Background(),
			wantErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			whiteBoardService := mock_service.NewMockIWhiteBoardService(ctrl)
			rslv := &resolver.Resolver{
				WhiteBoardService: whiteBoardService,
			}

			if tt.ctx != context.Background() {
				whiteBoardService.EXPECT().GetWhiteBoard(gomock.Any(), whiteBoardHospitalID).Return(tt.getWhiteBoardRes, tt.getWhiteBoardErr).AnyTimes()
			}

			got, err := rslv.Query().GetWhiteBoard(tt.ctx)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func Test_queryResolver_CheckContentOutdated(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	sess := &serviceModel.Session{
		HospitalID: &whiteBoardHospitalID,
		StaffID:    &whiteBoardStaffID,
	}
	ctx := test_mock.GetTestContextWithSession(sess)

	tests := []struct {
		name                    string
		ctx                     context.Context
		expectedContent         string
		checkContentOutdatedRes bool
		checkContentOutdatedErr error
		want                    bool
		wantErr                 error
	}{
		{
			name:                    "正常系：内容が最新（outdatedではない）",
			ctx:                     ctx,
			expectedContent:         "current content",
			checkContentOutdatedRes: false,
			want:                    false,
		},
		{
			name:                    "正常系：内容がoutdated",
			ctx:                     ctx,
			expectedContent:         "old content",
			checkContentOutdatedRes: true,
			want:                    true,
		},
		{
			name:                    "異常系：サービスエラー",
			ctx:                     ctx,
			expectedContent:         "content",
			checkContentOutdatedErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("service error")),
			wantErr:                 myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, fmt.Errorf("service error")),
		},
		{
			name:            "異常系：セッションなし",
			ctx:             context.Background(),
			expectedContent: "content",
			wantErr:         myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			whiteBoardService := mock_service.NewMockIWhiteBoardService(ctrl)
			rslv := &resolver.Resolver{
				WhiteBoardService: whiteBoardService,
			}

			if tt.ctx != context.Background() {
				expectedUserID := fmt.Sprintf("staff_%d", whiteBoardStaffID)
				whiteBoardService.EXPECT().CheckContentOutdated(gomock.Any(), whiteBoardHospitalID, expectedUserID, tt.expectedContent).Return(tt.checkContentOutdatedRes, tt.checkContentOutdatedErr).AnyTimes()
			}

			got, err := rslv.Query().CheckContentOutdated(tt.ctx, tt.expectedContent)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func Test_mutationResolver_EditWhiteBoard(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	sess := &serviceModel.Session{
		HospitalID: &whiteBoardHospitalID,
		StaffID:    &whiteBoardStaffID,
	}
	ctx := test_mock.GetTestContextWithSession(sess)

	tests := []struct {
		name                      string
		ctx                       context.Context
		contentBefore             string
		contentAfter              string
		editWhiteBoardWithUserErr error
		want                      bool
		wantErr                   error
	}{
		{
			name:          "正常系：WhiteBoardの編集成功",
			ctx:           ctx,
			contentBefore: "old content",
			contentAfter:  "new content",
			want:          true,
		},
		{
			name:                      "異常系：サービスエラー",
			ctx:                       ctx,
			contentBefore:             "old content",
			contentAfter:              "new content",
			editWhiteBoardWithUserErr: myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("content mismatch")),
			wantErr:                   myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, fmt.Errorf("content mismatch")),
		},
		{
			name:          "異常系：セッションなし",
			ctx:           context.Background(),
			contentBefore: "old content",
			contentAfter:  "new content",
			wantErr:       myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidSession, fmt.Errorf("context no session")),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			whiteBoardService := mock_service.NewMockIWhiteBoardService(ctrl)
			rslv := &resolver.Resolver{
				WhiteBoardService: whiteBoardService,
			}

			if tt.ctx != context.Background() {
				expectedUserID := fmt.Sprintf("staff_%d", whiteBoardStaffID)
				whiteBoardService.EXPECT().EditWhiteBoardWithUser(gomock.Any(), whiteBoardHospitalID, tt.contentBefore, tt.contentAfter, expectedUserID).Return(tt.editWhiteBoardWithUserErr).AnyTimes()
			}

			got, err := rslv.Mutation().EditWhiteBoard(tt.ctx, tt.contentBefore, tt.contentAfter)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}
