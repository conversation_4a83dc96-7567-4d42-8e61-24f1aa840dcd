package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen

import (
	"context"
	"denkaru-server/pkg/util/session"
	"fmt"
)

// EditWhiteBoard is the resolver for the editWhiteBoard field.
func (r *mutationResolver) EditWhiteBoard(ctx context.Context, contentBefore string, contentAfter string) (bool, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return false, err
	}

	// Convert staff ID to string for user identification
	currentUserID := "system"
	if sess.StaffID != nil {
		currentUserID = fmt.Sprintf("staff_%d", *sess.StaffID)
	}

	err = r.WhiteBoardService.EditWhiteBoardWithUser(ctx, *sess.HospitalID, contentBefore, contentAfter, currentUserID)
	if err != nil {
		return false, err
	}

	return true, nil
}

// GetWhiteBoard is the resolver for the getWhiteBoard field.
func (r *queryResolver) GetWhiteBoard(ctx context.Context) (string, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return "", err
	}

	whiteBoard, err := r.WhiteBoardService.GetWhiteBoard(ctx, *sess.HospitalID)
	if err != nil {
		return "", err
	}

	return whiteBoard, nil
}

// CheckContentOutdated is the resolver for the checkContentOutdated field.
func (r *queryResolver) CheckContentOutdated(ctx context.Context, expectedContent string) (bool, error) {
	sess, err := session.GetSession(ctx)
	if err != nil {
		return false, err
	}

	// Convert staff ID to string for user identification
	currentUserID := "system"
	if sess.StaffID != nil {
		currentUserID = fmt.Sprintf("staff_%d", *sess.StaffID)
	}

	isOutdated, err := r.WhiteBoardService.CheckContentOutdated(ctx, *sess.HospitalID, currentUserID, expectedContent)
	if err != nil {
		return false, err
	}

	return isOutdated, nil
}
