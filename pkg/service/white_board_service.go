package service

import (
	"context"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
)

type IWhiteBoardService interface {
	GetWhiteBoard(ctx context.Context, hospitalID int) (string, error)
	EditWhiteBoard(ctx context.Context, hospitalID int, contentBefore string, contentAfter string) error
}

type whiteBoardService struct {
	repo repository.IWhiteBoardLogRepository
}

func NewWhiteBoardService(repo repository.IWhiteBoardLogRepository) IWhiteBoardService {
	return &whiteBoardService{repo: repo}
}

func (s *whiteBoardService) GetWhiteBoard(ctx context.Context, hospitalID int) (string, error) {
	lastWhiteBoardLog, err := s.repo.GetLastWhiteBoardLog(ctx, hospitalID)
	if err != nil {
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return lastWhiteBoardLog, nil
}

func (s *whiteBoardService) EditWhiteBoard(ctx context.Context, hospitalID int, contentBefore string, contentAfter string) error {
	lastWhiteBoardLog, err := s.repo.GetLastWhiteBoardLog(ctx, hospitalID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if lastWhiteBoardLog != contentBefore {
		return myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, errors.New("contentBefore is not equal to lastWhiteBoardLog"), "contentBefore")
	}

	err = s.repo.CreateWhiteBoardLog(ctx, hospitalID, contentBefore, contentAfter)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}
