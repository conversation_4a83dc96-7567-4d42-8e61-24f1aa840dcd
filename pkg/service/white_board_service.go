//go:generate mockgen -source=$GOFILE -destination=../test_mock/$GOPACKAGE/$GOFILE
package service

import (
	"context"
	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/cockroachdb/errors"
)

type IWhiteBoardService interface {
	GetWhiteBoard(ctx context.Context, hospitalID int) (string, error)
	EditWhiteBoard(ctx context.Context, hospitalID int, contentBefore string, contentAfter string) error
	EditWhiteBoardWithUser(ctx context.Context, hospitalID int, contentBefore string, contentAfter string, userID string) error
	CheckContentOutdated(ctx context.Context, hospitalID int, currentUserID string, expectedContent string) (bool, error)
}

type whiteBoardService struct {
	repo repository.IWhiteBoardLogRepository
}

func NewWhiteBoardService(repo repository.IWhiteBoardLogRepository) IWhiteBoardService {
	return &whiteBoardService{repo: repo}
}

func (s *whiteBoardService) GetWhiteBoard(ctx context.Context, hospitalID int) (string, error) {
	lastWhiteBoardLog, err := s.repo.GetLastWhiteBoardLog(ctx, hospitalID)
	if err != nil {
		return "", errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return lastWhiteBoardLog, nil
}

func (s *whiteBoardService) EditWhiteBoard(ctx context.Context, hospitalID int, contentBefore string, contentAfter string) error {
	lastWhiteBoardLog, err := s.repo.GetLastWhiteBoardLog(ctx, hospitalID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if lastWhiteBoardLog != contentBefore {
		return myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, errors.New("contentBefore is not equal to lastWhiteBoardLog"), "contentBefore")
	}

	err = s.repo.CreateWhiteBoardLog(ctx, hospitalID, contentBefore, contentAfter)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *whiteBoardService) EditWhiteBoardWithUser(ctx context.Context, hospitalID int, contentBefore string, contentAfter string, userID string) error {
	lastWhiteBoardLog, err := s.repo.GetLastWhiteBoardLog(ctx, hospitalID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	if lastWhiteBoardLog != contentBefore {
		return myerrors.NewDenkaruError(definitions.DenkaruCodeInvalidParameter, errors.New("contentBefore is not equal to lastWhiteBoardLog"), "contentBefore")
	}

	err = s.repo.CreateWhiteBoardLogWithUser(ctx, hospitalID, contentBefore, contentAfter, userID)
	if err != nil {
		return errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	return nil
}

func (s *whiteBoardService) CheckContentOutdated(ctx context.Context, hospitalID int, currentUserID string, expectedContent string) (bool, error) {
	lastWhiteBoardLogDetail, err := s.repo.GetLastWhiteBoardLogDetail(ctx, hospitalID)
	if err != nil {
		return false, errors.Join(myerrors.NewDenkaruError(definitions.DenkaruCodeInternalServerError, err))
	}

	// Check if content has changed from what the user expects
	if lastWhiteBoardLogDetail.ContentAfter != expectedContent {
		return true, nil
	}

	// Check if the last change was made by a different user
	// If created_by is "system" or same as current user, content is not outdated
	if lastWhiteBoardLogDetail.CreatedBy == "system" || lastWhiteBoardLogDetail.CreatedBy == currentUserID {
		return false, nil
	}

	// Content was changed by another user
	return true, nil
}
