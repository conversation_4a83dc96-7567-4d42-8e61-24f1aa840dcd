package service

import (
	"context"
	"errors"
	"testing"

	"denkaru-server/pkg/myerrors"
	"denkaru-server/pkg/repository/model/entity"
	mock_repository "denkaru-server/pkg/test_mock/repository"

	"github.com/bizleap-healthcare/denkaru-codes/definitions"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestNewWhiteBoardService(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)

	service := NewWhiteBoardService(mockRepo)

	assert.NotNil(t, service)
	// Test interface implementation
	var _ IWhiteBoardService = service
}

func Test_whiteBoardService_GetWhiteBoard(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx        context.Context
		hospitalID int
	}

	tests := []struct {
		name      string
		args      args
		mockSetup func(*mock_repository.MockIWhiteBoardLogRepository)
		want      string
		wantErr   bool
	}{
		{
			name: "正常系：GetLastWhiteBoardLog",
			args: args{
				ctx:        context.Background(),
				hospitalID: 1,
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("test content", nil)
			},
			want:    "test content",
			wantErr: false,
		},
		{
			name: "エラー系：GetLastWhiteBoardLogでエラー",
			args: args{
				ctx:        context.Background(),
				hospitalID: 1,
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("", errors.New("db error"))
			},
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
			tt.mockSetup(mockRepo)

			s := NewWhiteBoardService(mockRepo)

			got, err := s.GetWhiteBoard(tt.args.ctx, tt.args.hospitalID)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_whiteBoardService_EditWhiteBoard(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx           context.Context
		hospitalID    int
		contentBefore string
		contentAfter  string
	}

	tests := []struct {
		name      string
		args      args
		mockSetup func(*mock_repository.MockIWhiteBoardLogRepository)
		wantErr   bool
		checkErr  func(error) bool
	}{
		{
			name: "正常系：編集前後で内容が異なる",
			args: args{
				ctx:           context.Background(),
				hospitalID:    1,
				contentBefore: "old content",
				contentAfter:  "new content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("old content", nil)
				mockRepo.EXPECT().CreateWhiteBoardLog(gomock.Any(), 1, "old content", "new content").Return(nil)
			},
			wantErr: false,
		},
		{
			name: "エラー系：GetLastWhiteBoardLogでエラー",
			args: args{
				ctx:           context.Background(),
				hospitalID:    1,
				contentBefore: "old content",
				contentAfter:  "new content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("", errors.New("db error"))
			},
			wantErr: true,
			checkErr: func(err error) bool {
				var denkaruErr *myerrors.DenkaruError
				if errors.As(err, &denkaruErr) {
					return denkaruErr.Code == definitions.DenkaruCodeInternalServerError.Code &&
						denkaruErr.ErrMessage == "db error"
				}
				return false
			},
		},
		{
			name: "エラー系：編集前後で内容が異なる",
			args: args{
				ctx:           context.Background(),
				hospitalID:    1,
				contentBefore: "old content",
				contentAfter:  "new content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("different content", nil)
			},
			wantErr: true,
			checkErr: func(err error) bool {
				var denkaruErr *myerrors.DenkaruError
				if errors.As(err, &denkaruErr) {
					return denkaruErr.Code == definitions.DenkaruCodeInvalidParameter.Code
				}
				return false
			},
		},
		{
			name: "エラー系：CreateWhiteBoardLogでエラー",
			args: args{
				ctx:           context.Background(),
				hospitalID:    1,
				contentBefore: "old content",
				contentAfter:  "new content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("old content", nil)
				mockRepo.EXPECT().CreateWhiteBoardLog(gomock.Any(), 1, "old content", "new content").Return(errors.New("create error"))
			},
			wantErr: true,
			checkErr: func(err error) bool {
				var denkaruErr *myerrors.DenkaruError
				if errors.As(err, &denkaruErr) {
					return denkaruErr.Code == definitions.DenkaruCodeInternalServerError.Code &&
						denkaruErr.ErrMessage == "create error"
				}
				return false
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
			tt.mockSetup(mockRepo)

			s := NewWhiteBoardService(mockRepo)

			err := s.EditWhiteBoard(tt.args.ctx, tt.args.hospitalID, tt.args.contentBefore, tt.args.contentAfter)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.checkErr != nil {
					assert.True(t, tt.checkErr(err), "Error validation failed: %v", err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_whiteBoardService_EdgeCases(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	t.Run("編集前後で内容が空のEditWhiteBoard", func(t *testing.T) {
		mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
		mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("", nil)
		mockRepo.EXPECT().CreateWhiteBoardLog(gomock.Any(), 1, "", "new content").Return(nil)

		s := NewWhiteBoardService(mockRepo)

		err := s.EditWhiteBoard(context.Background(), 1, "", "new content")
		assert.NoError(t, err)
	})

	t.Run("ホスピターIDが0のGetWhiteBoard", func(t *testing.T) {
		mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
		mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 0).Return("content", nil)

		s := NewWhiteBoardService(mockRepo)

		result, err := s.GetWhiteBoard(context.Background(), 0)
		assert.NoError(t, err)
		assert.Equal(t, "content", result)
	})

	t.Run("ホスピターIDが負のEditWhiteBoard", func(t *testing.T) {
		mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
		mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), -1).Return("content", nil)
		mockRepo.EXPECT().CreateWhiteBoardLog(gomock.Any(), -1, "content", "new content").Return(nil)

		s := NewWhiteBoardService(mockRepo)

		err := s.EditWhiteBoard(context.Background(), -1, "content", "new content")
		assert.NoError(t, err)
	})

	t.Run("編集前後で内容が同じ場合のEditWhiteBoard", func(t *testing.T) {
		mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
		mockRepo.EXPECT().GetLastWhiteBoardLog(gomock.Any(), 1).Return("same content", nil)
		mockRepo.EXPECT().CreateWhiteBoardLog(gomock.Any(), 1, "same content", "same content").Return(nil)

		s := NewWhiteBoardService(mockRepo)

		err := s.EditWhiteBoard(context.Background(), 1, "same content", "same content")
		assert.NoError(t, err)
	})
}

func Test_whiteBoardService_CheckContentOutdated(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		ctx             context.Context
		hospitalID      int
		currentUserID   string
		expectedContent string
	}

	tests := []struct {
		name      string
		args      args
		mockSetup func(*mock_repository.MockIWhiteBoardLogRepository)
		want      bool
		wantErr   bool
	}{
		{
			name: "正常系：内容が変更されていない場合（同じユーザー）",
			args: args{
				ctx:             context.Background(),
				hospitalID:      1,
				currentUserID:   "staff_123",
				expectedContent: "current content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLogDetail(gomock.Any(), 1).Return(&entity.HpWhiteBoardLog{
					HpID:         1,
					ContentAfter: "current content",
					CreatedBy:    "staff_123",
					UpdatedBy:    "staff_123",
				}, nil)
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "正常系：内容が変更されていない場合（システムユーザー）",
			args: args{
				ctx:             context.Background(),
				hospitalID:      1,
				currentUserID:   "staff_123",
				expectedContent: "current content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLogDetail(gomock.Any(), 1).Return(&entity.HpWhiteBoardLog{
					HpID:         1,
					ContentAfter: "current content",
					CreatedBy:    "system",
					UpdatedBy:    "system",
				}, nil)
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "正常系：内容が他のユーザーによって変更されている場合",
			args: args{
				ctx:             context.Background(),
				hospitalID:      1,
				currentUserID:   "staff_123",
				expectedContent: "old content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLogDetail(gomock.Any(), 1).Return(&entity.HpWhiteBoardLog{
					HpID:         1,
					ContentAfter: "new content",
					CreatedBy:    "staff_456",
					UpdatedBy:    "staff_456",
				}, nil)
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "正常系：同じ内容だが他のユーザーが最後に変更した場合",
			args: args{
				ctx:             context.Background(),
				hospitalID:      1,
				currentUserID:   "staff_123",
				expectedContent: "current content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLogDetail(gomock.Any(), 1).Return(&entity.HpWhiteBoardLog{
					HpID:         1,
					ContentAfter: "current content",
					CreatedBy:    "staff_456",
					UpdatedBy:    "staff_456",
				}, nil)
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "エラー系：リポジトリエラー",
			args: args{
				ctx:             context.Background(),
				hospitalID:      1,
				currentUserID:   "staff_123",
				expectedContent: "content",
			},
			mockSetup: func(mockRepo *mock_repository.MockIWhiteBoardLogRepository) {
				mockRepo.EXPECT().GetLastWhiteBoardLogDetail(gomock.Any(), 1).Return(nil, errors.New("database error"))
			},
			want:    false,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := mock_repository.NewMockIWhiteBoardLogRepository(ctrl)
			tt.mockSetup(mockRepo)

			s := NewWhiteBoardService(mockRepo)

			got, err := s.CheckContentOutdated(tt.args.ctx, tt.args.hospitalID, tt.args.currentUserID, tt.args.expectedContent)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.want, got)
		})
	}
}
