// Package repository ...
//
//go:generate mockgen -source=$GOFILE -destination=../test_mock/$GOPACKAGE/$GOFILE
package repository

import (
	"context"
	"denkaru-server/pkg/repository/model/entity"
)

// IWhiteBoardLogRepository interface of WhiteBoardLogRepository
type IWhiteBoardLogRepository interface {
	GetLastWhiteBoardLog(ctx context.Context, hospitalID int) (string, error)
	GetLastWhiteBoardLogDetail(ctx context.Context, hospitalID int) (*entity.HpWhiteBoardLog, error)
	CreateWhiteBoardLog(ctx context.Context, hospitalID int, contentBefore string, contentAfter string) error
	CreateWhiteBoardLogWithUser(ctx context.Context, hospitalID int, contentBefore string, contentAfter string, userID string) error
}
