# WhiteBoard API Documentation

## Overview
WhiteBoard API cho phép quản lý nội dung bảng trắng trong hệ thống bệnh viện, bao gồm việc kiểm tra xem nội dung có bị người khác thay đổi hay không.

## APIs

### 1. getWhiteBoard
**Type:** Query  
**Description:** Lấy nội dung hiện tại của WhiteBoard  
**Authentication:** Required

**GraphQL Query:**
```graphql
query {
  getWhiteBoard
}
```

**Response:**
- `String!`: Nội dung hiện tại của WhiteBoard

### 2. editWhiteBoard
**Type:** Mutation  
**Description:** Chỉnh sửa nội dung WhiteBoard  
**Authentication:** Required

**GraphQL Mutation:**
```graphql
mutation EditWhiteBoard($contentBefore: String!, $contentAfter: String!) {
  editWhiteBoard(contentBefore: $contentBefore, contentAfter: $contentAfter)
}
```

**Parameters:**
- `contentBefore` (String!): Nội dung trước khi chỉnh sửa
- `contentAfter` (String!): Nội dung sau khi chỉnh sửa

**Response:**
- `Boolean!`: `true` nếu chỉnh sửa thành công, `false` nếu thất bại

### 3. checkContentOutdated (NEW)
**Type:** Query  
**Description:** Kiểm tra xem nội dung WhiteBoard có bị người khác thay đổi không  
**Authentication:** Required

**GraphQL Query:**
```graphql
query CheckContentOutdated($expectedContent: String!) {
  checkContentOutdated(expectedContent: $expectedContent)
}
```

**Parameters:**
- `expectedContent` (String!): Nội dung mà client mong đợi

**Response:**
- `Boolean!`: 
  - `true` nếu nội dung đã bị người khác thay đổi (outdated)
  - `false` nếu nội dung vẫn là mới nhất

**Logic:**
1. So sánh `expectedContent` với nội dung hiện tại trong database
2. Nếu nội dung khác nhau → return `true` (outdated)
3. Nếu nội dung giống nhau nhưng người thay đổi cuối cùng khác với user hiện tại → return `true` (outdated)
4. Nếu nội dung giống nhau và người thay đổi cuối cùng là user hiện tại hoặc "system" → return `false` (not outdated)

## Use Cases

### Scenario 1: Kiểm tra trước khi chỉnh sửa
```graphql
# 1. Lấy nội dung hiện tại
query {
  getWhiteBoard
}

# 2. Kiểm tra xem có bị outdated không
query {
  checkContentOutdated(expectedContent: "nội dung client đang có")
}

# 3. Nếu không outdated, thực hiện chỉnh sửa
mutation {
  editWhiteBoard(
    contentBefore: "nội dung cũ"
    contentAfter: "nội dung mới"
  )
}
```

### Scenario 2: Polling để kiểm tra thay đổi
```javascript
// Định kỳ kiểm tra xem nội dung có bị thay đổi bởi người khác không
setInterval(async () => {
  const result = await client.query({
    query: CHECK_CONTENT_OUTDATED,
    variables: { expectedContent: currentContent }
  });
  
  if (result.data.checkContentOutdated) {
    // Nội dung đã bị thay đổi, cần refresh
    alert("Nội dung đã được cập nhật bởi người khác!");
    // Reload content
  }
}, 30000); // Kiểm tra mỗi 30 giây
```

## Error Handling

### Common Errors:
- `DenkaruCodeInvalidSession`: Session không hợp lệ
- `DenkaruCodeInternalServerError`: Lỗi server nội bộ
- `DenkaruCodeInvalidParameter`: Tham số không hợp lệ (cho editWhiteBoard)

## Database Schema

### hp_white_board_log Table:
- `hp_id`: ID bệnh viện
- `white_board_log_id`: ID log (primary key)
- `content_before`: Nội dung trước khi thay đổi
- `content_after`: Nội dung sau khi thay đổi
- `created_by`: User ID người tạo (format: "staff_{staffID}" hoặc "system")
- `updated_by`: User ID người cập nhật
- `created_at`: Thời gian tạo
- `updated_at`: Thời gian cập nhật
- `is_deleted`: Flag xóa (0: không xóa, 1: đã xóa)

## Implementation Notes

1. **User Identification**: User được identify bằng format "staff_{staffID}" hoặc "system"
2. **Concurrency Control**: API `checkContentOutdated` giúp detect conflict trước khi save
3. **Audit Trail**: Mọi thay đổi đều được log với thông tin user
4. **Session Management**: Tất cả API đều require authentication và sử dụng session để lấy hospital_id và staff_id
